# FairyGUI边距映射功能

## 功能说明

这个功能将FairyGUI的设计边距映射到Unity场景中的相机视图，通过绘制线条来可视化UI的安全区域。

## 设计参数

- **FairyGUI设计分辨率**: 1080x1920
- **上边距**: 300像素
- **下边距**: 500像素

## 使用方法

### 1. MapScene组件设置

在MapScene组件中，你可以找到以下设置：

- `Show UI Margins`: 是否显示边距线条
- `Margin Line Color`: 边距线条颜色（默认红色）
- `Margin Line Width`: 边距线条宽度

### 2. 运行时控制

添加UIMarginTester组件可以在运行时测试功能：

- 按T键切换边距线条显示
- 在屏幕左上角显示当前状态信息

### 3. 坐标转换原理

系统会自动：

1. 获取当前屏幕分辨率
2. 计算FairyGUI的缩放因子（保持宽高比）
3. 计算居中偏移量
4. 将FairyGUI像素坐标转换为Unity世界坐标
5. 在场景中绘制对应的线条

### 4. 实时更新

边距线条会在以下情况下自动更新：

- 相机位置改变
- 相机缩放改变
- 屏幕分辨率改变
- 游戏窗口大小改变

## 技术实现

### 核心方法

- `ConvertFairyGUIToWorldPosition()`: 坐标转换
- `CreateMarginLines()`: 创建线条对象
- `UpdateMarginLines()`: 更新线条位置
- `OnDrawGizmos()`: 编辑器中显示Gizmos

### 线条渲染

使用LineRenderer组件在运行时绘制线条，确保在游戏中可见。

## 设置步骤

1. 将MapScene脚本添加到场景中的GameObject上
2. 确保场景中有主相机
3. 在Inspector中配置MapScene组件：
   - 勾选"Show UI Margins"
   - 设置"Margin Line Color"（推荐使用红色或黄色）
   - 调整"Margin Line Width"（推荐2-5）
4. （可选）添加UIMarginTester脚本用于运行时测试

## 注意事项

1. 确保场景中有主相机
2. 线条会跟随相机移动和缩放
3. 可以通过Inspector面板调整线条颜色和宽度
4. 编辑器中也会显示Gizmos线条用于调试
5. 线条在运行时和编辑器中都可见
6. 支持动态切换显示状态
