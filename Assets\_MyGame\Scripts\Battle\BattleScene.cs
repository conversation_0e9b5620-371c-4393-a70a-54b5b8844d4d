using UnityEngine;
using UnityEngine.Rendering.Universal;

public class BattleScene : MonoBehaviour
{
    public Camera mainCamera;
    private void Awake()
    {
        if (Camera.main != mainCamera)
        {
            mainCamera.GetUniversalAdditionalCameraData().renderType = CameraRenderType.Overlay;
            var addCameraData = Camera.main.GetUniversalAdditionalCameraData();
            if (addCameraData.cameraStack != null && !addCameraData.cameraStack.Contains(mainCamera))
            {
                addCameraData.cameraStack.Insert(0, mainCamera);
            }
        }
    }

    private void OnDestroy()
    {
        if (Camera.main != null)
        {
            var cameraStack = Camera.main.GetUniversalAdditionalCameraData().cameraStack;
            if (cameraStack != null && cameraStack.Contains(mainCamera))
            {
                cameraStack.Remove(mainCamera);
            }
        }
    }
}