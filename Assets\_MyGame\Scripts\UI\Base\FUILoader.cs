using FairyGUI;
using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Pool;

public class FUILoader
{
    public static float packageAutoDisposeTime = 5 * 60f; // 包自动销毁时间（秒）
    private static HashSet<string> _protectedPackages = new HashSet<string>(); // 受保护的包名，不会被自动卸载

    /// <summary>
    /// 添加受保护包名
    /// </summary>
    public static void AddProtectedPackage(string packageName)
    {
        if (!_protectedPackages.Contains(packageName))
        {
            _protectedPackages.Add(packageName);
        }
    }

    /// <summary>
    /// 移除受保护包名
    /// </summary>
    public static void RemoveProtectedPackage(string packageName)
    {
        _protectedPackages.Remove(packageName);
    }

    /// <summary>
    /// 检查包名是否受保护
    /// </summary>
    public static bool IsPackageProtected(string packageName)
    {
        return _protectedPackages.Contains(packageName);
    }

    /// <summary>
    /// 检查间隔（s）,为0则不检查
    /// </summary>
#if UNITY_EDITOR
    public static float checkInterval = 1f;
#else
    public static float checkInterval = 60f;
#endif

    private static Dictionary<string, PackageInfo> _uiPackageInfoDic = new Dictionary<string, PackageInfo>();

    // PackageInfo对象池
    private static ObjectPool<PackageInfo> _packageInfoPool = new ObjectPool<PackageInfo>(
        createFunc: () => new PackageInfo(),
        actionOnGet: (info) => info.Reset(),
        actionOnRelease: null,
        actionOnDestroy: null,
        collectionCheck: false,
        defaultCapacity: 30,
        maxSize: 100
    );

    /// <summary>
    /// 包自动卸载事件
    /// </summary>
    public static event Action<string> OnPackageAutoUnloaded;

    public static void LoadPackage(string packname, string[] extraPacks, Action doneAction = null, Action<float> onProgress = null, bool needRelease = false)
    {
        if (extraPacks == null)
        {
            LoadPackage(packname, doneAction, onProgress, needRelease);
        }
        else
        {
            var tempPackNames = ListPool<string>.Get();
            tempPackNames.Add(packname);
            tempPackNames.AddRange(extraPacks);
            LoadPackages(tempPackNames, doneAction, onProgress, needRelease);
            ListPool<string>.Release(tempPackNames);
        }
    }

    public static void LoadPackage(string packname, Action doneAction = null, Action<float> onProgress = null, bool needRelease = false)
    {
        if (UIPackage.GetByName(packname) == null)
        {
            AddPackage(packname, doneAction, onProgress, needRelease);
        }
        else
        {
            onProgress?.Invoke(1);
            doneAction?.Invoke();
        }
    }

    public static void LoadPackages(List<string> packNames, Action doneAction, Action<float> onProgress = null, bool needRelease = false)
    {
        var count = packNames.Count;
        if (count == 0)
        {
            doneAction.Invoke();
            return;
        }

        var loaded = 0;
        for (int i = 0; i < count; i++)
        {
            LoadPackage(packNames[i], () =>
            {
                loaded++;
                if (loaded == count)
                {
                    doneAction.Invoke();
                }
            }, (radio) =>
            {
                onProgress?.Invoke((loaded + radio) / count);
            }, needRelease);
        }
    }
    public static void IncreaseRef(string pkgName, string compName)
    {
        if (_uiPackageInfoDic.TryGetValue(pkgName, out var info))
        {
            info.IncreaseRef(compName);
        }
    }

    public static void DecreaseRef(string pkgName, string compName)
    {
        if (_uiPackageInfoDic.TryGetValue(pkgName, out var info))
        {
            info.DecreaseRef(compName);
        }
    }

    private static float preCheckTime = 0;
    public static void UnloadUnuseAssetsLoop()
    {
        if (Time.realtimeSinceStartup - preCheckTime > checkInterval)
        {
            preCheckTime = Time.realtimeSinceStartup;
            UnloadUnuseAssets();
        }
    }

    private static List<PackageInfo> waitDelPkgList = new List<PackageInfo>(100);
    private static bool needDelPkg;
    public static void UnloadUnuseAssets()
    {
        if (checkInterval == 0)
            return;

        needDelPkg = false;
        var realtimeSinceStartup = Time.realtimeSinceStartup;
        foreach (var item in _uiPackageInfoDic)
        {
            var pkgInfo = item.Value;
            if (pkgInfo.CanDispose(realtimeSinceStartup) && !IsPackageProtected(pkgInfo.pkgName))
            {
                waitDelPkgList.Add(pkgInfo);
                needDelPkg = true;
            }
        }

        foreach (var pkgInfo in waitDelPkgList)
        {
            string packageName = pkgInfo.pkgName;
            _uiPackageInfoDic.Remove(packageName);
            UIPackage.RemovePackage(packageName);
            pkgInfo.Release();

            // 将PackageInfo返回对象池
            _packageInfoPool.Release(pkgInfo);

            // 触发包自动卸载事件
            OnPackageAutoUnloaded?.Invoke(packageName);
        }
        waitDelPkgList.Clear();

        if (needDelPkg)
        {
            AssetBundleManager.UnloadUnuseAssets();
        }
    }

    private static void AddPackage(string pkgName, Action doneAction, Action<float> onProgress = null, bool needRelease = true)
    {
        if (!AssetBundleManager.IsInitialize)
        {
            Log.Warning("AddPackage:" + pkgName + "  AssetBundleManager.IsInitialize:False");
            return;
        }

        var packInfo = AddPackInfo(pkgName, needRelease);
        var url = GetFuiUrl(pkgName);

        var uiHandle = AssetBundleManager.LoadBytes(url, (byte[] bytes) =>
        {
            var loadItems = new List<LoadInfo>();
            if (bytes == null || bytes.Length == 0)
            {
                doneAction?.Invoke();
                return;
            }

            UIPackage.AddPackage(bytes, pkgName, (name, extension, type, item) =>
            {
                if (type == typeof(Texture))
                {
                    var tName = "UI/" + pkgName + "/" + name;
                    var loadInfo = new LoadInfo();
                    loadInfo.path = tName;
                    loadInfo.item = item;
                    loadItems.Add(loadInfo);
                }
            });

            var uiPack = UIPackage.GetByName(pkgName);
            var items = uiPack.GetItems();
            for (int i = 0; i < items.Count; i++)
            {
                var item = items[i];
                switch (item.type)
                {

                    case PackageItemType.Spine:
                        if (item.skeletonAsset == null)
                        {
                            var sName = "UI/" + pkgName + "/" + item.name + "_SkeletonData";
                            var loadInfo = new LoadInfo();
                            loadInfo.path = sName;
                            loadInfo.item = item;
                            loadItems.Add(loadInfo);
                        }
                        break;
                    default:
                        uiPack.GetItemAsset(item);
                        break;
                }
            }

            var curLoaded = 0;
            var loadTotal = loadItems.Count;
            if (loadTotal == 0)
            {
                doneAction?.Invoke();
            }
            else
            {
                for (int i = 0; i < loadTotal; i++)
                {
                    var loadInfo = loadItems[i];
                    var handle = AssetBundleManager.LoadObject(loadInfo.path, (obj) =>
                    {
                        loadInfo.item.owner.SetItemAsset(loadInfo.item, obj, DestroyMethod.None);
                        curLoaded++;
                        onProgress?.Invoke((float)curLoaded / loadTotal);
                        if (curLoaded == loadTotal)
                        {
                            doneAction?.Invoke();
                        }
                    });
                    packInfo?.AddAssetHandle(handle);
                }
            }
        });
        packInfo?.AddAssetHandle(uiHandle);
    }

    private static string GetFuiUrl(string pkgName)
    {
        return "UI/" + pkgName + "/" + pkgName + "_fui";
    }

    private static PackageInfo AddPackInfo(string packName, bool needRelease)
    {
        if (!_uiPackageInfoDic.ContainsKey(packName))
        {
            // 从对象池获取PackageInfo
            var resInfo = _packageInfoPool.Get();
            resInfo.pkgName = packName;
            _uiPackageInfoDic.Add(packName, resInfo);
        }
        return _uiPackageInfoDic[packName];
    }

    #region 资源引用管理
    protected class LoadInfo
    {
        public string path;
        public PackageItem item;
    }

    protected class PackageInfo
    {
        public string pkgName;
        public float lastUseTime;
        private List<AssetBundleManager.AssetHandle> assetHandles;
        // 将used改为公开，以便外部接口访问
        public bool used;
        // 将引用计数改为公开，以便外部接口访问
        public int referenceCount;
        public Dictionary<string, int> componentRefCounts;

        public PackageInfo()
        {
            assetHandles = new List<AssetBundleManager.AssetHandle>();
            componentRefCounts = new Dictionary<string, int>();
        }

        // 重置方法，用于对象池回收时重置状态
        public void Reset()
        {
            pkgName = null;
            lastUseTime = 0;
            used = false;
            referenceCount = 0;
            assetHandles.Clear();
            componentRefCounts.Clear();
        }
        public bool CanDispose(float realtimeSinceStartup)
        {
            var notingRef = referenceCount == 0;
            return used && notingRef && realtimeSinceStartup - lastUseTime >= packageAutoDisposeTime;
        }

        public void Release()
        {
            used = false;
            referenceCount = 0;
            lastUseTime = 0;
            for (int i = 0; i < assetHandles.Count; i++)
            {
                AssetBundleManager.Release(assetHandles[i]);
            }
            assetHandles.Clear();
        }

        public void AddAssetHandle(AssetBundleManager.AssetHandle handle)
        {
            assetHandles.Add(handle);
        }
        public void IncreaseRef(string compName)
        {
            used = true;
            referenceCount++;
            lastUseTime = Time.realtimeSinceStartup;

            // 记录组件引用计数
            if (!componentRefCounts.ContainsKey(compName))
            {
                componentRefCounts[compName] = 0;
            }
            componentRefCounts[compName]++;
        }
        public void DecreaseRef(string compName)
        {
            if (referenceCount > 0)
            {
                referenceCount--;
            }
            else
            {
                Log.Debug($"[UI] Warning:  DecreaseRef {pkgName}.{compName} referenceCount<0");
            }
            lastUseTime = Time.realtimeSinceStartup;

            // 更新组件引用计数
            if (componentRefCounts.ContainsKey(compName) && componentRefCounts[compName] > 0)
            {
                var compRefCount = componentRefCounts[compName];
                compRefCount--;
                if (compRefCount <= 0)
                {
                    componentRefCounts.Remove(compName);
                }
                else
                {
                    componentRefCounts[compName] = compRefCount;
                }
            }
        }
    }
    #endregion

    #region 资源监控接口

    /// <summary>
    /// 获取所有已加载的UI包信息
    /// </summary>
    /// <returns>UI包名称列表</returns>
    public static List<string> GetAllLoadedPackages()
    {
        List<string> result = new List<string>();
        foreach (var pair in _uiPackageInfoDic)
        {
            result.Add(pair.Key);
        }
        return result;
    }

    /// <summary>
    /// 获取指定UI包的引用计数
    /// </summary>
    /// <param name="packageName">UI包名称</param>
    /// <returns>引用计数，如果包不存在则返回-1</returns>
    public static int GetPackageReferenceCount(string packageName)
    {
        if (_uiPackageInfoDic.TryGetValue(packageName, out var info))
        {
            return info.referenceCount;
        }
        return -1;
    }

    /// <summary>
    /// 获取指定UI包的使用状态
    /// </summary>
    /// <param name="packageName">UI包名称</param>
    /// <returns>是否正在使用，如果包不存在则返回false</returns>
    public static bool IsPackageUsed(string packageName)
    {
        if (_uiPackageInfoDic.TryGetValue(packageName, out var info))
        {
            return info.used;
        }
        return false;
    }

    /// <summary>
    /// 获取指定UI包的组件引用计数信息
    /// </summary>
    /// <param name="packageName">UI包名称</param>
    /// <returns>组件引用计数字典，如果包不存在则返回null</returns>
    public static Dictionary<string, int> GetPackageComponentRefCounts(string packageName)
    {
        if (_uiPackageInfoDic.TryGetValue(packageName, out PackageInfo pkgInfo))
        {
            return pkgInfo.componentRefCounts;
        }
        return null;
    }

    // 获取包的最后使用时间
    public static float GetPackageLastUseTime(string packageName)
    {
        if (_uiPackageInfoDic.TryGetValue(packageName, out PackageInfo pkgInfo))
        {
            return pkgInfo.lastUseTime;
        }
        return 0f;
    }

    /// <summary>
    /// 检查UI包是否已加载
    /// </summary>
    /// <param name="packageName">UI包名称</param>
    /// <returns>是否已加载</returns>
    public static bool IsPackageLoaded(string packageName)
    {
        return UIPackage.GetByName(packageName) != null;
    }

    #endregion
}