using UnityEngine;

public class UIMarginTester : MonoBehaviour
{
    [Header("测试设置")]
    public bool enableTesting = true;
    public KeyCode toggleKey = KeyCode.T;

    private MapScene mapScene;

    void Start()
    {
        mapScene = FindObjectOfType<MapScene>();
        if (mapScene == null)
        {
            Debug.LogError("未找到MapScene组件！");
        }
    }

    void Update()
    {
        if (!enableTesting || mapScene == null) return;

        if (Input.GetKeyDown(toggleKey))
        {
            // 切换边距线条显示
            mapScene.showUIMargins = !mapScene.showUIMargins;
            Debug.Log($"UI边距线条显示: {mapScene.showUIMargins}");
        }
    }

    void OnGUI()
    {
        if (!enableTesting) return;

        GUILayout.BeginArea(new Rect(10, 10, 300, 200));
        GUILayout.Label("FairyGUI边距映射测试");
        GUILayout.Label($"按 {toggleKey} 键切换边距线条显示");

        if (mapScene != null)
        {
            GUILayout.Label($"当前显示状态: {mapScene.showUIMargins}");
            GUILayout.Label($"屏幕分辨率: {Screen.width}x{Screen.height}");

            // 显示FairyGUI相关信息
            GUILayout.Label("FairyGUI设计分辨率: 1080x1920");
            GUILayout.Label("上边距: 300像素");
            GUILayout.Label("下边距: 500像素");
        }

        GUILayout.EndArea();
    }
}
